import '../../../models/app_user.dart';
import '../../../models/document_model.dart';
import '../../../utils/enums.dart';

abstract class ValidationContentRepository {
  Future<List<AppUser>> getPendingValidations();
  Future<void> approveUser(String userId);
  Future<void> rejectUser(String userId, String reason);
  Future<void> startValidation(String userId);
}

class ValidationContentRepositoryImpl implements ValidationContentRepository {
  @override
  Future<List<AppUser>> getPendingValidations() async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Mock data for pending validations
    return [
      AppUser(
        id: '1',
        name: 'Dr. <PERSON>',
        email: '<EMAIL>',
        phone: '(11) 99999-9999',
        type: 'Profissional',
        specialty: 'Fisioterapia',
        location: 'São Paulo, SP',
        status: UserStatusEnum.pending,
        appointments: 0,
        lastAccess: DateTime.now().subtract(const Duration(days: 1)),
        documents: [
          DocumentModel(
            id: '1',
            title: 'Documento de Identidade',
            url: '#',
            status: DocumentStatus.pending,
          ),
          DocumentModel(
            id: '2',
            title: 'Certificado Profissional',
            url: '#',
            status: DocumentStatus.pending,
          ),
        ],
      ),
      AppUser(
        id: '2',
        name: 'Ana Beatriz Silva',
        email: '<EMAIL>',
        phone: '(11) 88888-8888',
        type: 'Paciente',
        specialty: null,
        location: 'Rio de Janeiro, RJ',
        status: UserStatusEnum.pending,
        appointments: 0,
        lastAccess: DateTime.now().subtract(const Duration(days: 2)),
        documents: [
          DocumentModel(
            id: '3',
            title: 'Documento de Identidade',
            url: '#',
            status: DocumentStatus.pending,
          ),
          DocumentModel(
            id: '4',
            title: 'Comprovante de Residência',
            url: '#',
            status: DocumentStatus.pending,
          ),
        ],
      ),
    ];
  }

  @override
  Future<void> approveUser(String userId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    // TODO: Implement API call
  }

  @override
  Future<void> rejectUser(String userId, String reason) async {
    await Future.delayed(const Duration(milliseconds: 500));
    // TODO: Implement API call
  }

  @override
  Future<void> startValidation(String userId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    // TODO: Implement API call
  }
}