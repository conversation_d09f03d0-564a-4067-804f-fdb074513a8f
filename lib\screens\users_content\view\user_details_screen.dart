import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/models/app_user.dart';
import 'package:homecare_nexus_clone/utils/app_text_theme.dart';
import 'package:homecare_nexus_clone/utils/app_theme.dart';
import 'package:homecare_nexus_clone/utils/extensions.dart';
import 'package:homecare_nexus_clone/widgets/tag_widget.dart';

class UserDetailsScreen extends StatelessWidget {
  final AppUser user;

  const UserDetailsScreen({super.key, required this.user});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Detalhes - ${user.name}'),
        backgroundColor: AppTheme.primaryGreen,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(user.name, style: textXl),
            const SizedBox(height: 8),
            Text(user.email, style: textSm.copyWith(color: AppTheme.textGray)),
            const SizedBox(height: 12),
            <PERSON>(
              children: [
                TagWidget(title: user.type),
                const SizedBox(width: 8),
                TagWidget(title: user.status.displayName),
              ],
            ),
            const SizedBox(height: 16),
            const Text('Localização', style: textLg),
            const SizedBox(height: 6),
            Text(user.location, style: textSm),
            const SizedBox(height: 12),
            if (user.specialty != null) ...[
              const Text('Especialidade', style: textLg),
              const SizedBox(height: 6),
              Text(user.specialty!, style: textSm),
              const SizedBox(height: 12),
            ],
            const Text('Documentos e Certificados', style: textLg),
            const SizedBox(height: 8),
            if (user.documents.isEmpty)
              const Text('Nenhum documento cadastrado', style: textSm)
            else
              Column(
                children: user.documents.map((d) {
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: const Icon(Icons.insert_drive_file),
                      title: Row(
                        children: [
                          Expanded(child: Text(d.title)),
                          const SizedBox(width: 8),
                          TagWidget(
                            title: d.status.displayName,
                            type: d.status.tagType,
                          ),
                        ],
                      ),
                      subtitle: Text(d.status.displayName,
                          style: textSm.copyWith(color: AppTheme.textGray)),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            onPressed: () {},
                            icon: const Icon(Icons.file_download_outlined),
                          ),
                          IconButton(
                            onPressed: () {},
                            icon: const Icon(Icons.visibility_outlined),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }
}
