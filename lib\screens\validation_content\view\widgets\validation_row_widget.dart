import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/models/app_user.dart';
import 'package:homecare_nexus_clone/utils/date_formatter.dart';
import 'package:homecare_nexus_clone/widgets/buttons/primary_button.dart';
import 'package:homecare_nexus_clone/widgets/tag_widget.dart';

import '../../../../core/global_instances.dart';
import '../../../../utils/app_theme.dart';
import '../../../../utils/enums.dart';
import '../validation_dialog.dart';

class ValidationRowWidget extends StatelessWidget {
  final AppUser user;

  const ValidationRowWidget({
    super.key,
    required this.user,
  });

  List<String> _getPendingFields(AppUser user) {
    return [
      'Nome do Usuário',
      'Informações Adicionais',
      ...user.documents.map((d) => d.title),
    ];
  }

  String _getStatusText(AppUser user) {
    switch (user.status) {
      case UserStatusEnum.pending:
        return 'Aguardando';
      case UserStatusEnum.inReview:
        return 'Em Análise';
      case UserStatusEnum.rejected:
        return 'Rejeitado';
      case UserStatusEnum.pendingUpdate:
        return 'Atualização Pendente';
      default:
        return 'Aguardando';
    }
  }

  IconData _getStatusIcon(AppUser user) {
    switch (user.status) {
      case UserStatusEnum.pending:
        return Icons.access_time;
      case UserStatusEnum.inReview:
        return Icons.visibility;
      case UserStatusEnum.rejected:
        return Icons.cancel_outlined;
      case UserStatusEnum.pendingUpdate:
        return Icons.update;
      default:
        return Icons.access_time;
    }
  }

  @override
  Widget build(BuildContext context) {
    final pendingFields = _getPendingFields(user);
    final statusText = _getStatusText(user);
    final statusIcon = _getStatusIcon(user);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppTheme.borderGray),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                    Text(
                      user.email,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.textGray.withValues(alpha: 0.7),
                      ),
                    ),
                    if (user.specialty != null)
                      Text(
                        user.specialty!,
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textGray.withValues(alpha: 0.7),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
              flex: 1,
              child: Align(
                  alignment: Alignment.centerLeft,
                  child: TagWidget(title: user.type))),
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: pendingFields
                  .map((field) => Padding(
                        padding: const EdgeInsets.only(bottom: 2),
                        child: Row(
                          children: [
                            Container(
                              width: 4,
                              height: 4,
                              decoration: const BoxDecoration(
                                color: AppTheme.primaryBlue,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                field,
                                style: const TextStyle(fontSize: 12),
                              ),
                            ),
                          ],
                        ),
                      ))
                  .toList(),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              DateFormatter.formatDate(user.lastAccess),
              style: const TextStyle(fontSize: 12),
            ),
          ),
          Expanded(
              flex: 1,
              child: Align(
                  alignment: Alignment.centerLeft,
                  child: TagWidget(
                      icon: Icon(statusIcon, size: 12), title: statusText))),
          Expanded(
            flex: 2,
            child: Center(
              child: CustomButton(
                onPressed: () {
                  validationContentCubit.startValidation(user.id);
                  showDialog(
                    context: context,
                    builder: (_) => ValidationDialog(
                      title: 'Validação de Usuário - ${user.name}',
                      subtitle:
                          'Analise todos os dados e documentos do usuário para aprovar ou rejeitar o cadastro',
                      name: user.name,
                      phone: user.phone,
                      address: user.location,
                      documents: user.documents,
                    ),
                  );
                },
                icon: const Icon(Icons.lock_outline, size: 16),
                label: 'Iniciar Validação',
                type: ButtonType.secondary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
