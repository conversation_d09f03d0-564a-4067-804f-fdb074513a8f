import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/models/app_user.dart';
import 'package:homecare_nexus_clone/utils/date_formatter.dart';
import 'package:homecare_nexus_clone/widgets/buttons/primary_button.dart';
import 'package:homecare_nexus_clone/widgets/tag_widget.dart';

import '../../../../core/global_instances.dart';
import '../../../../utils/app_theme.dart';
import '../../../../utils/enums.dart';
import '../validation_dialog.dart';

class ValidationRowWidget extends StatelessWidget {
  final AppUser user;

  const ValidationRowWidget({
    super.key,
    required this.user,
  });

  List<String> _getPendingFields(AppUser user) {
    return [
      'Nome do Usuário',
      'Informações Adicionais',
      ...user.documents.map((d) => d.title),
    ];
  }

  String _getStatusText(AppUser user) {
    switch (user.status) {
      case UserStatusEnum.pending:
        return 'Novo Cadastro';
      case UserStatusEnum.inReview:
        return 'Em Análise';
      case UserStatusEnum.active:
        return 'Aprovado';
      case UserStatusEnum.rejected:
        return 'Rejeitado';
      case UserStatusEnum.pendingUpdate:
        return 'Alteração Pendente';
      default:
        return 'Novo Cadastro';
    }
  }

  TagType _getStatusTagType(AppUser user) {
    switch (user.status) {
      case UserStatusEnum.pending:
        return TagType.border;
      case UserStatusEnum.inReview:
        return TagType.secondary;
      case UserStatusEnum.active:
        return TagType.primary;
      case UserStatusEnum.rejected:
        return TagType.error;
      case UserStatusEnum.pendingUpdate:
        return TagType.secondary;
      default:
        return TagType.border;
    }
  }

  IconData _getStatusIcon(AppUser user) {
    switch (user.status) {
      case UserStatusEnum.pending:
        return Icons.fiber_new;
      case UserStatusEnum.inReview:
        return Icons.visibility;
      case UserStatusEnum.active:
        return Icons.check_circle;
      case UserStatusEnum.rejected:
        return Icons.cancel;
      case UserStatusEnum.pendingUpdate:
        return Icons.update;
      default:
        return Icons.fiber_new;
    }
  }

  String _getButtonText(AppUser user) {
    switch (user.status) {
      case UserStatusEnum.pending:
        return 'Iniciar Validação';
      case UserStatusEnum.inReview:
        return 'Continuar Validação';
      case UserStatusEnum.active:
        return 'Visualizar';
      case UserStatusEnum.rejected:
        return 'Visualizar';
      case UserStatusEnum.pendingUpdate:
        return 'Continuar Validação';
      default:
        return 'Iniciar Validação';
    }
  }

  IconData _getButtonIcon(AppUser user) {
    switch (user.status) {
      case UserStatusEnum.pending:
        return Icons.play_arrow;
      case UserStatusEnum.inReview:
        return Icons.edit;
      case UserStatusEnum.active:
        return Icons.visibility;
      case UserStatusEnum.rejected:
        return Icons.visibility;
      case UserStatusEnum.pendingUpdate:
        return Icons.edit;
      default:
        return Icons.play_arrow;
    }
  }

  ButtonType _getButtonType(AppUser user) {
    switch (user.status) {
      case UserStatusEnum.pending:
        return ButtonType.primary;
      case UserStatusEnum.inReview:
        return ButtonType.secondary;
      case UserStatusEnum.active:
        return ButtonType.secondary;
      case UserStatusEnum.rejected:
        return ButtonType.secondary;
      case UserStatusEnum.pendingUpdate:
        return ButtonType.secondary;
      default:
        return ButtonType.primary;
    }
  }

  String _getDialogSubtitle(AppUser user) {
    switch (user.status) {
      case UserStatusEnum.pending:
        return 'Analise todos os dados e documentos do usuário para aprovar ou rejeitar o cadastro';
      case UserStatusEnum.inReview:
        return 'Continue a análise dos dados e documentos do usuário';
      case UserStatusEnum.active:
        return 'Visualize os dados aprovados do usuário';
      case UserStatusEnum.rejected:
        return 'Visualize os dados rejeitados e os motivos da rejeição';
      case UserStatusEnum.pendingUpdate:
        return 'Analise as alterações pendentes nos dados do usuário';
      default:
        return 'Analise todos os dados e documentos do usuário para aprovar ou rejeitar o cadastro';
    }
  }

  @override
  Widget build(BuildContext context) {
    final pendingFields = _getPendingFields(user);
    final statusText = _getStatusText(user);
    final statusTagType = _getStatusTagType(user);
    final statusIcon = _getStatusIcon(user);
    final buttonText = _getButtonText(user);
    final buttonIcon = _getButtonIcon(user);
    final buttonType = _getButtonType(user);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppTheme.borderGray),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                    Text(
                      user.email,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.textGray.withValues(alpha: 0.7),
                      ),
                    ),
                    if (user.specialty != null)
                      Text(
                        user.specialty!,
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textGray.withValues(alpha: 0.7),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
              flex: 1,
              child: Align(
                  alignment: Alignment.centerLeft,
                  child: TagWidget(title: user.type))),
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: pendingFields
                  .map((field) => Padding(
                        padding: const EdgeInsets.only(bottom: 2),
                        child: Row(
                          children: [
                            Container(
                              width: 4,
                              height: 4,
                              decoration: const BoxDecoration(
                                color: AppTheme.primaryBlue,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                field,
                                style: const TextStyle(fontSize: 12),
                              ),
                            ),
                          ],
                        ),
                      ))
                  .toList(),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              DateFormatter.formatDate(user.lastAccess),
              style: const TextStyle(fontSize: 12),
            ),
          ),
          Expanded(
              flex: 1,
              child: Align(
                  alignment: Alignment.centerLeft,
                  child: TagWidget(
                      icon: Icon(statusIcon, size: 12),
                      title: statusText,
                      type: statusTagType))),
          Expanded(
            flex: 2,
            child: Center(
              child: CustomButton(
                onPressed: () {
                  // Só inicia validação se for novo cadastro ou alteração pendente
                  if (user.status == UserStatusEnum.pending ||
                      user.status == UserStatusEnum.inReview ||
                      user.status == UserStatusEnum.pendingUpdate) {
                    validationContentCubit.startValidation(user.id);
                  }

                  showDialog(
                    context: context,
                    builder: (_) => ValidationDialog(
                      title: 'Validação de Usuário - ${user.name}',
                      subtitle: _getDialogSubtitle(user),
                      name: user.name,
                      phone: user.phone,
                      address: user.location,
                      documents: user.documents,
                    ),
                  );
                },
                icon: Icon(buttonIcon, size: 16),
                label: buttonText,
                type: buttonType,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
