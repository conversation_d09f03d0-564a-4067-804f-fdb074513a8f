import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:homecare_nexus_clone/models/document_model.dart';
import 'package:homecare_nexus_clone/utils/enums.dart';

part 'app_user.g.dart';

@CopyWith()
class AppUser {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String type; // 'Profissional' | 'Paciente'
  final String? specialty;
  final String location;
  final UserStatusEnum status;
  final DocumentStatus validationStatus;
  final int appointments;
  final DateTime lastAccess;
  final List<DocumentModel> documents;

  AppUser({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.type,
    this.specialty,
    required this.location,
    required this.status,
    this.validationStatus = DocumentStatus.pending,
    required this.appointments,
    required this.lastAccess,
    this.documents = const [],
  });

  factory AppUser.fromJson(Map<String, dynamic> json) {
    final docs = (json['documents'] as List<dynamic>?)
            ?.map((d) => DocumentModel.fromJson(d as Map<String, dynamic>))
            .toList() ??
        [];
    return AppUser(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      type: json['type'] ?? 'Paciente',
      specialty: json['specialty'],
      location: json['location'] ?? '',
      status: json['status'] ?? 'active',
      validationStatus: DocumentStatus.pending,
      appointments: json['appointments'] ?? 0,
      lastAccess: DateTime.tryParse(json['lastAccess'] ?? '') ?? DateTime.now(),
      documents: docs,
    );
  }
}
