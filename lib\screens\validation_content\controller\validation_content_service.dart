import '../../../models/app_user.dart';
import '../../../utils/enums.dart';
import 'validation_content_repository.dart';

class ValidationContentService {
  final ValidationContentRepository _repository;

  ValidationContentService(this._repository);

  Future<List<AppUser>> getPendingValidations() async {
    return await _repository.getPendingValidations();
  }

  Future<void> approveUser(String userId) async {
    await _repository.approveUser(userId);
  }

  Future<void> rejectUser(String userId, String reason) async {
    await _repository.rejectUser(userId, reason);
  }

  Future<void> startValidation(String userId) async {
    await _repository.startValidation(userId);
  }

  List<AppUser> filterValidations(List<AppUser> validations, String filter) {
    switch (filter) {
      case 'Novos Cadastros':
        // Novos cadastros: sem nada avaliado ainda
        return validations
            .where((u) => u.status == UserStatusEnum.pending)
            .toList();
      case 'Alterações Pendentes':
        // Alterações pendentes: que começaram a ser avaliadas
        return validations
            .where((u) =>
                u.status == UserStatusEnum.inReview ||
                u.status == UserStatusEnum.pendingUpdate)
            .toList();
      case 'Minhas Análises':
        // Minhas análises: que já foram aprovados (só visualizar)
        return validations
            .where((u) => u.status == UserStatusEnum.active)
            .toList();
      case 'Cadastros Recusados':
        // Cadastros recusados: só visualizar
        return validations
            .where((u) => u.status == UserStatusEnum.rejected)
            .toList();
      default:
        return validations;
    }
  }
}
