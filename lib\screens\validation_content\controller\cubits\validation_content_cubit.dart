import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:homecare_nexus_clone/models/app_user.dart';
import '../../../../utils/enums.dart';
import '../validation_content_service.dart';
import 'validation_content_state.dart';

class ValidationContentCubit extends Cubit<ValidationContentState> {
  final ValidationContentService _service;

  ValidationContentCubit(this._service) : super(const ValidationContentState()) {
    loadValidations();
  }

  // Add public getter for service access
  ValidationContentService get service => _service;

  Future<void> loadValidations() async {
    emit(state.copyWith(isLoading: true, error: null));
    
    try {
      final validations = await _service.getPendingValidations();
      final filtered = _service.filterValidations(validations, state.selectedFilter);
      
      emit(state.copyWith(
        all: validations,
        filtered: filtered,
        isLoading: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  void applyFilter(String filter) {
    final filtered = _service.filterValidations(state.all, filter);
    emit(state.copyWith(
      selectedFilter: filter,
      filtered: filtered,
    ));
  }

  Future<void> approveUser(String userId) async {
    try {
      await _service.approveUser(userId);
      
      // Update local state
      final updatedAll = state.all.map((u) {
        if (u.id == userId) {
          return u.copyWith(status: UserStatusEnum.active);
        }
        return u;
      }).toList();
      
      final filtered = _service.filterValidations(updatedAll, state.selectedFilter);
      emit(state.copyWith(all: updatedAll, filtered: filtered));
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> rejectUser(String userId, String reason) async {
    try {
      await _service.rejectUser(userId, reason);
      
      // Update local state
      final updatedAll = state.all.map((u) {
        if (u.id == userId) {
          return u.copyWith(status: UserStatusEnum.suspended);
        }
        return u;
      }).toList();
      
      final filtered = _service.filterValidations(updatedAll, state.selectedFilter);
      emit(state.copyWith(all: updatedAll, filtered: filtered));
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> startValidation(String userId) async {
    try {
      await _service.startValidation(userId);
      
      // Update local state
      final updatedAll = state.all.map((u) {
        if (u.id == userId) {
          return u.copyWith(status: UserStatusEnum.inReview);
        }
        return u;
      }).toList();
      
      final filtered = _service.filterValidations(updatedAll, state.selectedFilter);
      emit(state.copyWith(all: updatedAll, filtered: filtered));
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> refresh() async => await loadValidations();
}
