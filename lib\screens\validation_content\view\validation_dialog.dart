import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/models/document_model.dart';
import 'package:homecare_nexus_clone/utils/app_colors.dart';
import 'package:homecare_nexus_clone/widgets/buttons/primary_button.dart';
import 'package:homecare_nexus_clone/widgets/containers/container_bordered.dart';
import 'package:homecare_nexus_clone/widgets/tag_widget.dart';
import 'package:homecare_nexus_clone/widgets/textfields/text_field_custom.dart';

import '../../../utils/app_text_theme.dart';
import '../../../utils/app_theme.dart';
import '../../../utils/enums.dart';
import '../../../utils/extensions.dart';

/// Componente reutilizável para informações gerais
class InfoItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;

  const InfoItem({
    super.key,
    required this.icon,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 16),
        const SizedBox(width: 8),
        Text(label, style: textSm.copyWith(fontWeight: FontWeight.w600)),
        const SizedBox(width: 6),
        Flexible(
          child: Text(
            value,
            style: textSm.copyWith(color: AppTheme.textGray),
          ),
        ),
      ],
    );
  }
}

/// Card de Dados Pessoais
class PersonalDataCard extends StatelessWidget {
  final String name;
  final String? additionalInfo;
  final DocumentStatus status;
  final VoidCallback onApprove;
  final VoidCallback onReject;
  final String? rejectionReason;

  const PersonalDataCard({
    super.key,
    required this.name,
    this.additionalInfo,
    required this.status,
    required this.onApprove,
    required this.onReject,
    this.rejectionReason,
  });

  @override
  Widget build(BuildContext context) {
    return ContainerBordered(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  const Icon(Icons.person, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'Nome do Usuário',
                    style: textMd.copyWith(color: foreground),
                  ),
                ],
              ),
              status.tag(),
            ],
          ),
          const SizedBox(height: 8),
          Text(name, style: textSm),
          if (additionalInfo != null && additionalInfo!.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(additionalInfo!,
                style: textSm.copyWith(color: AppTheme.textGray)),
          ],
          const SizedBox(height: 12),
          if (status == DocumentStatus.rejected &&
              rejectionReason != null &&
              rejectionReason!.isNotEmpty)
            Container(
              decoration: BoxDecoration(
                color: errorColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: errorColor.withOpacity(0.2)),
              ),
              padding: const EdgeInsets.all(8),
              width: double.infinity,
              child: Text('Motivo da rejeição: ${rejectionReason!}',
                  style: textSm.copyWith(
                    color: errorColor,
                    fontWeight: FontWeight.w700,
                  )),
            )
          else if (status == DocumentStatus.approved)
            const SizedBox.shrink()
          else
            Row(
              children: [
                CustomButton(
                  onPressed: onApprove,
                  icon: const Icon(Icons.check_circle_outline, size: 16),
                  label: 'Aprovar',
                ),
                const SizedBox(width: 8),
                CustomButton(
                  type: ButtonType.error,
                  onPressed: onReject,
                  icon: const Icon(Icons.cancel_outlined, size: 16),
                  label: 'Rejeitar',
                ),
              ],
            ),
        ],
      ),
    );
  }
}

/// Card de Documento ou Certificado
class DocumentCard extends StatelessWidget {
  final String title;
  final DocumentStatus status;
  final String? rejectionReason;
  final VoidCallback onApprove;
  final VoidCallback onReject;
  final VoidCallback onDownload;
  final VoidCallback onView;

  const DocumentCard({
    super.key,
    required this.title,
    required this.status,
    this.rejectionReason,
    required this.onApprove,
    required this.onReject,
    required this.onDownload,
    required this.onView,
  });

  @override
  Widget build(BuildContext context) {
    return ContainerBordered(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.insert_drive_file, size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: textMd.copyWith(color: foreground),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              status.tag(),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              CustomButton(
                type: ButtonType.secondary,
                icon: const Icon(Icons.file_download_outlined, size: 16),
                onPressed: onDownload,
                label: 'Baixar',
              ),
              const SizedBox(width: 8),
              CustomButton(
                type: ButtonType.secondary,
                icon: const Icon(Icons.visibility_outlined, size: 16),
                onPressed: onView,
                label: 'Visualizar',
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (status == DocumentStatus.rejected &&
              rejectionReason != null &&
              rejectionReason!.isNotEmpty)
            Text('Motivo da rejeição: ${rejectionReason!}',
                style: textSm.copyWith(
                  color: errorColor,
                  fontWeight: FontWeight.w700,
                ))
          else if (status == DocumentStatus.approved)
            const SizedBox.shrink()
          else
            Row(
              children: [
                CustomButton(
                  onPressed: onApprove,
                  icon: const Icon(Icons.check_circle_outline, size: 16),
                  label: 'Aprovar',
                ),
                const SizedBox(width: 8),
                CustomButton(
                  type: ButtonType.error,
                  onPressed: onReject,
                  icon: const Icon(Icons.cancel_outlined, size: 16),
                  label: 'Rejeitar',
                ),
              ],
            ),
        ],
      ),
    );
  }
}

/// Dialog principal
class ValidationDialog extends StatefulWidget {
  final String title;
  final String subtitle;
  final String name;
  final String? additionalInfo;
  final String phone;
  final String address;
  final List<DocumentModel> documents;

  const ValidationDialog({
    super.key,
    required this.title,
    required this.subtitle,
    required this.name,
    this.additionalInfo,
    required this.phone,
    required this.address,
    required this.documents,
  });

  @override
  State<ValidationDialog> createState() => _ValidationDialogState();
}

class _ValidationDialogState extends State<ValidationDialog> {
  DocumentStatus _personalStatus = DocumentStatus.pending;
  late List<DocumentStatus> _docStatuses;
  String? _personalRejectionReason;
  late List<String?> _docRejectionReasons;

  @override
  void initState() {
    super.initState();
    _docStatuses = widget.documents.map((doc) {
      return doc.status ?? DocumentStatus.pending;
    }).toList();
    _docRejectionReasons = List<String?>.filled(_docStatuses.length, null);
  }

  Future<void> _showRejectionReasonDialog({
    required BuildContext context,
    required ValueChanged<String> onConfirm,
  }) async {
    final TextEditingController controller = TextEditingController();
    await showDialog<bool>(
      context: context,
      builder: (context) {
        return Dialog(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 450),
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Rejeitar Informações Adicionais',
                        style: textLg),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Informe o motivo da rejeição. O usuário será notificado após a análise completa.',
                  style: textSm.copyWith(fontWeight: FontWeight.w400),
                ),
                const SizedBox(height: 12),
                TextFieldCustom(
                  controller: controller,
                  maxLines: 4,
                  hintText:
                      'Ex: Documento ilegível, data vencida, informações inconsistentes...',
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    CustomButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      label: 'Cancelar',
                      type: ButtonType.secondary,
                    ),
                    const SizedBox(width: 8),
                    CustomButton(
                      onPressed: () {
                        if (controller.text.trim().isNotEmpty) {
                          onConfirm(controller.text.trim());
                          Navigator.of(context).pop(true);
                        }
                      },
                      label: 'Confirmar Rejeição',
                      type: ButtonType.error,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
    controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 850, maxHeight: 700),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(widget.title, style: textXl),
                        const SizedBox(height: 6),
                        Text(widget.subtitle,
                            style: textSm.copyWith(color: AppTheme.textGray)),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: Navigator.of(context).pop,
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              ContainerBordered(
                child: Wrap(
                  runSpacing: 16,
                  spacing: 16,
                  children: [
                    InfoItem(
                        icon: Icons.person, label: 'Nome:', value: widget.name),
                    if (widget.additionalInfo != null &&
                        widget.additionalInfo!.isNotEmpty)
                      InfoItem(
                        icon: Icons.info,
                        label: 'Informações Adicionais:',
                        value: widget.additionalInfo!,
                      ),
                    InfoItem(
                        icon: Icons.phone,
                        label: 'Telefone:',
                        value: widget.phone),
                    InfoItem(
                        icon: Icons.home,
                        label: 'Endereço:',
                        value: widget.address),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              const Text('Dados Pessoais', style: textLg),
              const SizedBox(height: 8),
              PersonalDataCard(
                name: widget.name,
                additionalInfo: widget.additionalInfo,
                status: _personalStatus,
                onApprove: () =>
                    setState(() => _personalStatus = DocumentStatus.approved),
                onReject: () async {
                  await _showRejectionReasonDialog(
                    context: context,
                    onConfirm: (reason) => setState(() {
                      _personalStatus = DocumentStatus.rejected;
                      _personalRejectionReason = reason;
                    }),
                  );
                },
                rejectionReason: _personalRejectionReason,
              ),
              const SizedBox(height: 12),
              const Text('Documentos', style: textLg),
              const SizedBox(height: 8),
              Column(
                children: widget.documents.asMap().entries.map((entry) {
                  final index = entry.key;
                  final doc = entry.value;
                  return DocumentCard(
                    title: doc.title.isNotEmpty ? doc.title : 'Documento',
                    status: _docStatuses[index],
                    onApprove: () => setState(
                        () => _docStatuses[index] = DocumentStatus.approved),
                    onReject: () async {
                      await _showRejectionReasonDialog(
                        context: context,
                        onConfirm: (reason) => setState(() {
                          _docStatuses[index] = DocumentStatus.rejected;
                          _docRejectionReasons[index] = reason;
                        }),
                      );
                    },
                    rejectionReason: _docRejectionReasons[index],
                    onDownload: () {},
                    onView: () {},
                  );
                }).toList(),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  CustomButton(
                    onPressed: Navigator.of(context).pop,
                    label: 'Fechar',
                    type: ButtonType.secondary,
                  ),
                  if (_docStatuses.any(
                      (status) => status == DocumentStatus.rejected)) ...[
                    const SizedBox(width: 16),
                    CustomButton(
                      onPressed: Navigator.of(context).pop,
                      label: 'Cadastro Rejeitado',
                      type: ButtonType.error,
                    )
                  ],
                  if (!_docStatuses.any(
                      (status) => status == DocumentStatus.rejected)) ...[
                    const SizedBox(width: 16),
                    CustomButton(
                      onPressed: () {},
                      label: 'Aprovar Cadastro',
                      type: ButtonType.primary,
                    ),
                  ]
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
