import '../../../../models/app_user.dart';

class UserState {
  final List<AppUser> all;
  final List<AppUser> filtered;
  final String filter; // 'Todos', 'Profissionais', 'Pacientes', etc
  final String search;

  const UserState({
    this.all = const [],
    this.filtered = const [],
    this.filter = 'Todos',
    this.search = '',
  });

  UserState copyWith({
    List<AppUser>? all,
    List<AppUser>? filtered,
    String? filter,
    String? search,
  }) {
    return UserState(
      all: all ?? this.all,
      filtered: filtered ?? this.filtered,
      filter: filter ?? this.filter,
      search: search ?? this.search,
    );
  }
}
