import 'package:get_it/get_it.dart';
import 'package:homecare_nexus_clone/screens/administrators_content/controller/administrators_content_repository.dart';
import 'package:homecare_nexus_clone/screens/administrators_content/controller/administrators_content_service.dart';
import 'package:homecare_nexus_clone/screens/administrators_content/controller/cubits/administrators_content_cubit.dart';
import 'package:homecare_nexus_clone/screens/users_content/controller/cubits/users_content_cubit.dart';
import 'package:homecare_nexus_clone/screens/users_content/controller/users_content_repository.dart';
import 'package:homecare_nexus_clone/screens/users_content/controller/users_content_service.dart';

final getIt = GetIt.instance;

void init() {
  // Repositories
  getIt.registerLazySingleton<UserRepository>(() => UserRepository());
  getIt.registerLazySingleton<AdministratorsContentRepository>(
      () => AdministratorsContentRepository());

  // Services
  getIt.registerLazySingleton<UserContentService>(
      () => UserContentService(getIt()));
  getIt.registerLazySingleton<AdministratorsContentService>(
      () => AdministratorsContentService(getIt()));

  // Cubits
  getIt.registerSingleton<UserContentCubit>(UserContentCubit(getIt()));
  getIt.registerSingleton<AdministratorsContentCubit>(
      AdministratorsContentCubit(getIt()));
}
