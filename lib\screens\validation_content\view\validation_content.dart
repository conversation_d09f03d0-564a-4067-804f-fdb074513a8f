import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:homecare_nexus_clone/models/app_user.dart';
import 'package:homecare_nexus_clone/models/document_model.dart';
import 'package:homecare_nexus_clone/utils/app_colors.dart';
import 'package:homecare_nexus_clone/utils/date_formatter.dart';
import 'package:homecare_nexus_clone/widgets/buttons/primary_button.dart';
import 'package:homecare_nexus_clone/widgets/containers/content_container.dart';
import 'package:homecare_nexus_clone/widgets/filter_chip_widget.dart';
import 'package:homecare_nexus_clone/widgets/tag_widget.dart';

import '../../../core/global_instances.dart';
import '../../../utils/app_theme.dart';
import '../../../utils/enums.dart';
import '../../../widgets/stat_card.dart';
import '../controller/cubits/validation_content_cubit.dart';
import '../controller/cubits/validation_content_state.dart';
import 'validation_dialog.dart';

class ValidationContent extends StatefulWidget {
  const ValidationContent({super.key});

  @override
  State<ValidationContent> createState() => _ValidationContentState();
}

class _ValidationContentState extends State<ValidationContent> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ValidationContentCubit, ValidationContentState>(
      bloc: validationContentCubit,
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Expanded(
                    child: StatCard(
                      title: 'Novos Cadastros',
                      value: '2',
                      valueColor: AppTheme.primaryBlue,
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: StatCard(
                      title: 'Alterações Pendentes',
                      value: '0',
                      valueColor: Colors.orange,
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: StatCard(
                      title: 'Em Análise (Você)',
                      value: '0',
                      valueColor: AppTheme.primaryPurple,
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: StatCard(
                      title: 'Cadastros Recusados',
                      value: '1',
                      valueColor: AppTheme.errorRed,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32),
              ContentContainer(
                title: 'Lista de Validações',
                subtitle:
                    'Gerencie validações de documentos com controle de concorrência',
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: background,
                    ),
                    child: Row(
                      children: [
                        FilterChipWidget(
                          label:
                              'Novos Cadastros (${_getFilterCount('Novos Cadastros')})',
                          value: 'Novos Cadastros',
                          isSelected: state.selectedFilter == 'Novos Cadastros',
                          onSelected: (selected) {
                            validationContentCubit.applyFilter(selected);
                          },
                        ),
                        const SizedBox(width: 12),
                        FilterChipWidget(
                          label:
                              'Alterações Pendentes (${_getFilterCount('Alterações Pendentes')})',
                          value: 'Alterações Pendentes',
                          isSelected:
                              state.selectedFilter == 'Alterações Pendentes',
                          onSelected: (selected) {
                            validationContentCubit.applyFilter(selected);
                          },
                        ),
                        const SizedBox(width: 12),
                        FilterChipWidget(
                          label:
                              'Minhas Análises (${_getFilterCount('Minhas Análises')})',
                          value: 'Minhas Análises',
                          isSelected: state.selectedFilter == 'Minhas Análises',
                          onSelected: (selected) {
                            validationContentCubit.applyFilter(selected);
                          },
                        ),
                        const SizedBox(width: 12),
                        FilterChipWidget(
                          label:
                              'Cadastros Recusados (${_getFilterCount('Cadastros Recusados')})',
                          value: 'Cadastros Recusados',
                          isSelected:
                              state.selectedFilter == 'Cadastros Recusados',
                          onSelected: (selected) {
                            validationContentCubit.applyFilter(selected);
                          },
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  Column(
                    children: [
                      // Header da tabela
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(color: AppTheme.borderGray),
                          ),
                        ),
                        child: const Row(
                          children: [
                            Expanded(
                                flex: 3,
                                child: Text('Usuário',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w600))),
                            Expanded(
                                flex: 1,
                                child: Text('Tipo',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w600))),
                            Expanded(
                                flex: 3,
                                child: Text('Campos Pendentes',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w600))),
                            Expanded(
                                flex: 2,
                                child: Text('Submetido em',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w600))),
                            Expanded(
                                flex: 1,
                                child: Text('Status',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w600))),
                            Expanded(
                                flex: 2,
                                child: Text('Ações',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w600))),
                          ],
                        ),
                      ),
                      ...state.filtered.map((user) => _buildValidationRow(
                            name: user.name,
                            email: user.email,
                            specialty: user.specialty,
                            type: user.type,
                            pendingFields: _getPendingFields(user),
                            submittedAt:
                                DateFormatter.formatDate(user.lastAccess),
                            status: 'Aguardando',
                            statusColor: Colors.orange,
                            avatarColor: AppTheme.primaryBlue,
                            userId: user.id,
                          )),
                    ],
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  int _getFilterCount(String filter) {
    final state = validationContentCubit.state;
    return validationContentCubit.service
        .filterValidations(state.all, filter)
        .length;
  }

  List<String> _getPendingFields(AppUser user) {
    return [
      'Nome do Usuário',
      'Informações Adicionais',
      ...user.documents.map((d) => d.title),
    ];
  }

  Widget _buildValidationRow({
    required String userId,
    required String name,
    required String email,
    String? specialty,
    required String type,
    required List<String> pendingFields,
    required String submittedAt,
    required String status,
    required Color statusColor,
    required Color avatarColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppTheme.borderGray),
        ),
      ),
      child: Row(
        children: [
          // Usuário
          Expanded(
            flex: 3,
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                    Text(
                      email,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.textGray.withValues(alpha: 0.7),
                      ),
                    ),
                    if (specialty != null)
                      Text(
                        specialty,
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textGray.withValues(alpha: 0.7),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
              flex: 1,
              child: Align(
                  alignment: Alignment.centerLeft,
                  child: TagWidget(title: type))),
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: pendingFields
                  .map((field) => Padding(
                        padding: const EdgeInsets.only(bottom: 2),
                        child: Row(
                          children: [
                            Container(
                              width: 4,
                              height: 4,
                              decoration: const BoxDecoration(
                                color: AppTheme.primaryBlue,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                field,
                                style: const TextStyle(fontSize: 12),
                              ),
                            ),
                          ],
                        ),
                      ))
                  .toList(),
            ),
          ),

          // Submetido em
          Expanded(
            flex: 2,
            child: Text(
              submittedAt,
              style: const TextStyle(fontSize: 12),
            ),
          ),
          Expanded(
              flex: 1,
              child: Align(
                  alignment: Alignment.centerLeft,
                  child: TagWidget(
                      icon: status == 'Aguardando'
                          ? const Icon(Icons.access_time, size: 12)
                          : const Icon(Icons.check_circle_outline, size: 12),
                      title: status))),

          // Ações
          Expanded(
            flex: 2,
            child: Center(
              child: CustomButton(
                onPressed: () {
                  validationContentCubit.startValidation(userId);
                  showDialog(
                    context: context,
                    builder: (_) => ValidationDialog(
                      title: 'Validação de Usuário - $name',
                      subtitle:
                          'Analise todos os dados e documentos do usuário para aprovar ou rejeitar o cadastro',
                      name: name,
                      phone: '(11) 99999-9999',
                      address: 'Rua Exemplo, 123 - Centro',
                      documents: [
                        DocumentModel(
                            id: '1',
                            title: 'Documento de Identidade',
                            url: '#',
                            status: DocumentStatus.pending),
                        DocumentModel(
                            id: '2',
                            title: 'Comprovante de Residência',
                            url: '#',
                            status: DocumentStatus.pending),
                        DocumentModel(
                            id: '3',
                            title: 'Certificado Profissional',
                            url: '#',
                            status: DocumentStatus.pending),
                      ],
                    ),
                  );
                },
                icon: const Icon(Icons.lock_outline, size: 16),
                label: 'Iniciar Validação',
                type: ButtonType.secondary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
